<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.web.大数据需求.mapper.DatatestMapper">
    
    <resultMap type="Datatest" id="DatatestResult">
        <result property="id"    column="id"    />
        <result property="belong"    column="belong"    />
        <result property="industry"    column="industry"    />
        <result property="whiteList"    column="white_list"    />
        <result property="modelName"    column="model_name"    />
        <result property="magnitude"    column="magnitude"    />
        <result property="host"    column="host"    />
        <result property="urlPattern"    column="url_pattern"    />
        <result property="ageRange"    column="age_range"    />
        <result property="provId"    column="prov_id"    />
        <result property="freq"    column="freq"    />
        <result property="freqMin"    column="freq_min"    />
        <result property="freqMax"    column="freq_max"    />
        <result property="gender"    column="gender"    />
        <result property="city"    column="city"    />
        <result property="category"    column="category"    />
        <result property="ruleType"    column="rule_type"    />
    </resultMap>

    <sql id="selectDatatestVo">
        select belong, industry, white_list, model_name, magnitude, host, url_pattern, age_range, prov_id, freq, freq_min, freq_max, gender, city, category, rule_type from datatest
    </sql>

    <select id="selectDatatestList" parameterType="Datatest" resultMap="DatatestResult">
        <include refid="selectDatatestVo"/>
        <where>  
            <if test="belong != null  and belong != ''"> and belong = #{belong}</if>
            <if test="industry != null  and industry != ''"> and industry = #{industry}</if>
            <if test="whiteList != null  and whiteList != ''"> and white_list = #{whiteList}</if>
            <if test="modelName != null  and modelName != ''"> and model_name like concat('%', #{modelName}, '%')</if>
            <if test="magnitude != null "> and magnitude = #{magnitude}</if>
            <if test="host != null  and host != ''"> and host = #{host}</if>
            <if test="urlPattern != null  and urlPattern != ''"> and url_pattern = #{urlPattern}</if>
            <if test="ageRange != null  and ageRange != ''"> and age_range = #{ageRange}</if>
            <if test="provId != null "> and prov_id = #{provId}</if>
            <if test="freq != null  and freq != ''"> and freq = #{freq}</if>
            <if test="freqMin != null  and freqMin != ''"> and freq_min = #{freqMin}</if>
            <if test="freqMax != null  and freqMax != ''"> and freq_max = #{freqMax}</if>
            <if test="gender != null  and gender != ''"> and gender = #{gender}</if>
            <if test="city != null  and city != ''"> and city = #{city}</if>
            <if test="category != null  and category != ''"> and category = #{category}</if>
            <if test="ruleType != null  and ruleType != ''"> and rule_type = #{ruleType}</if>
        </where>
    </select>
    
    <select id="selectDatatestById" parameterType="Long" resultMap="DatatestResult">
        <include refid="selectDatatestVo"/>
        where id = #{id}
    </select>

    <insert id="insertDatatest" parameterType="Datatest">
        insert into datatest
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="belong != null">belong,</if>
            <if test="industry != null">industry,</if>
            <if test="whiteList != null">white_list,</if>
            <if test="modelName != null">model_name,</if>
            <if test="magnitude != null">magnitude,</if>
            <if test="host != null">host,</if>
            <if test="urlPattern != null">url_pattern,</if>
            <if test="ageRange != null">age_range,</if>
            <if test="provId != null">prov_id,</if>
            <if test="freq != null">freq,</if>
            <if test="freqMin != null">freq_min,</if>
            <if test="freqMax != null">freq_max,</if>
            <if test="gender != null">gender,</if>
            <if test="city != null">city,</if>
            <if test="category != null">category,</if>
            <if test="ruleType != null">rule_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="belong != null">#{belong},</if>
            <if test="industry != null">#{industry},</if>
            <if test="whiteList != null">#{whiteList},</if>
            <if test="modelName != null">#{modelName},</if>
            <if test="magnitude != null">#{magnitude},</if>
            <if test="host != null">#{host},</if>
            <if test="urlPattern != null">#{urlPattern},</if>
            <if test="ageRange != null">#{ageRange},</if>
            <if test="provId != null">#{provId},</if>
            <if test="freq != null">#{freq},</if>
            <if test="freqMin != null">#{freqMin},</if>
            <if test="freqMax != null">#{freqMax},</if>
            <if test="gender != null">#{gender},</if>
            <if test="city != null">#{city},</if>
            <if test="category != null">#{category},</if>
            <if test="ruleType != null">#{ruleType},</if>
         </trim>
    </insert>
    <insert id="batchSave">
        INSERT INTO datatest (
        industry,
        model_name,
        magnitude,
        host,
        url_pattern,
        category,
        belong,
        white_list,
        age_range,
        prov_id,
        freq,
        freq_min,
        freq_max,
        gender,
        city,
        rule_type,
        black_list
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.industry},
            #{item.modelName},
            #{item.magnitude},
            #{item.host},
            #{item.urlPattern},
            #{item.category},
            #{item.belong},
            #{item.whiteList},
            #{item.ageRange},
            #{item.provId},
            #{item.freq},
            #{item.freqMin},
            #{item.freqMax},
            #{item.gender},
            #{item.city},
            #{item.ruleType},
            #{item.blackList},
            )
        </foreach>
    </insert>

    <update id="updateDatatest" parameterType="Datatest">
        update datatest
        <trim prefix="SET" suffixOverrides=",">
            <if test="belong != null">belong = #{belong},</if>
            <if test="industry != null">industry = #{industry},</if>
            <if test="whiteList != null">white_list = #{whiteList},</if>
            <if test="modelName != null">model_name = #{modelName},</if>
            <if test="magnitude != null">magnitude = #{magnitude},</if>
            <if test="host != null">host = #{host},</if>
            <if test="urlPattern != null">url_pattern = #{urlPattern},</if>
            <if test="ageRange != null">age_range = #{ageRange},</if>
            <if test="provId != null">prov_id = #{provId},</if>
            <if test="freq != null">freq = #{freq},</if>
            <if test="freqMin != null">freq_min = #{freqMin},</if>
            <if test="freqMax != null">freq_max = #{freqMax},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="city != null">city = #{city},</if>
            <if test="category != null">category = #{category},</if>
            <if test="ruleType != null">rule_type = #{ruleType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDatatestById" parameterType="Long">
        delete from datatest where id = #{id}
    </delete>

    <delete id="deleteDatatestByIds" parameterType="String">
        delete from datatest where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>